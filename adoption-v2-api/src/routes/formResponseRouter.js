const express = require("express");
const router = express.Router();
const FormResponseController = require("../controllers/FormResponseController");
const auth = require("../middleware/authMiddleware");
const validatePagination = require("../middleware/validatePagination");
const { check } = require("express-validator");

// Form yanıtı oluşturma
router.post(
  "/",
  [
    auth(),
    [
      check("formId", "Form ID required").not().isEmpty(),
      check("title", "Title required").not().isEmpty(),
      check("responses", "Responses required").isArray(),
      check("responses.*.fieldId", "Field ID required").not().isEmpty(),
      check("responses.*.name", "Field name required").not().isEmpty(),
      check("responses.*.type", "Field type required").not().isEmpty(),
      check("responses.*.value", "Field value required").not().isEmpty(),
    ],
  ],
  FormResponseController.create
);

// Form yanıtlarını listeleme
router.get("/", [auth(), validatePagination], FormResponseController.list);

// Belirli bir form yanıtını görüntüleme
router.get("/:id", auth(), FormResponseController.get);

// Form yanıtını güncelleme
router.put("/:id", auth(), FormResponseController.update);

// Form yanıtını silme
router.delete("/:id", auth(), FormResponseController.delete);

module.exports = router;
